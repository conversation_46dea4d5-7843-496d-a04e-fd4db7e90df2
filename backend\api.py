# from flask import Flask, request, jsonify
# from database import Database
# import jwt
# import datetime
# from functools import wraps

# app = Flask(__name__)
# app.config['SECRET_KEY'] = 'your-secret-key-here'
# db = Database()

# # Authentication decorator
# def token_required(f):
#     @wraps(f)
#     def decorated(*args, **kwargs):
#         token = request.headers.get('Authorization')
#         if not token:
#             return jsonify({'message': 'Token is missing!'}), 403
        
#         try:
#             data = jwt.decode(token.split()[1], app.config['SECRET_KEY'], algorithms=["HS256"])
#             current_user = data['user_id']
#         except:
#             return jsonify({'message': 'Token is invalid!'}), 403
        
#         return f(current_user, *args, **kwargs)
#     return decorated

# # User Routes
# @app.route('/register', methods=['POST'])
# def register():
#     data = request.get_json()
#     user_id = db.register_user(data['email'], data['password'], data['name'])
#     if user_id:
#         return jsonify({'message': 'User registered successfully', 'user_id': user_id})
#     return jsonify({'message': 'Email already exists'}), 400

# @app.route('/login', methods=['POST'])
# def login():
#     data = request.get_json()
#     user = db.authenticate_user(data['email'], data['password'])
#     if user:
#         token = jwt.encode({
#             'user_id': user['user_id'],
#             'exp': datetime.datetime.utcnow() + datetime.timedelta(hours=24)
#         }, app.config['SECRET_KEY'])
#         return jsonify({'token': token, 'name': user['name']})
#     return jsonify({'message': 'Invalid credentials'}), 401

# # Quiz Routes
# @app.route('/quizzes', methods=['POST'])
# @token_required
# def create_quiz(current_user):
#     data = request.get_json()
#     quiz_id = db.create_quiz(data['title'], data['description'], current_user)
#     return jsonify({'quiz_id': quiz_id, 'message': 'Quiz created successfully'})

# @app.route('/quizzes/<int:quiz_id>/questions', methods=['POST'])
# @token_required
# def add_question(current_user, quiz_id):
#     data = request.get_json()
#     question_id = db.add_question(quiz_id, data['question_text'], data['options'], data['correct_answer'])
#     return jsonify({'question_id': question_id, 'message': 'Question added successfully'})

# # Quiz Taking Routes
# @app.route('/quizzes/<int:quiz_id>', methods=['GET'])
# def get_quiz(quiz_id):
#     quiz = db.get_quiz(quiz_id)
#     if quiz:
#         questions = db.get_quiz_questions(quiz_id)
#         return jsonify({'quiz': quiz, 'questions': questions})
#     return jsonify({'message': 'Quiz not found'}), 404

# @app.route('/submit', methods=['POST'])
# @token_required
# def submit_quiz(current_user):
#     data = request.get_json()
#     submission_id = db.submit_quiz(current_user, data['quiz_id'], data['score'], data['answers'])
#     return jsonify({'submission_id': submission_id, 'message': 'Quiz submitted successfully'})

# if __name__ == '__main__':
#     app.run(debug=True, port=5000)

from flask import Flask, jsonify, request, CORS

app = Flask(__name__)
CORS(app)  # Enable Cross-Origin Requests

# Example API endpoint
@app.route('/api/quizzes', methods=['GET'])
def get_quizzes():
    quizzes = [{"id": 1, "title": "Python Quiz"}]
    return jsonify(quizzes)

@app.route('/api/submit', methods=['POST'])
def submit_quiz():
    data = request.json  # Data from frontend
    print("Received:", data)
    return jsonify({"status": "success"})

if __name__ == '__main__':
    app.run(port=5000)  # Runs on http://localhost:5000