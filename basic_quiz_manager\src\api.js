// // src/services/api.js
// export const createUser = async (userData) => {
//     const response = await fetch('http://localhost:8000/users', {
//       method: 'POST',
//       headers: { 'Content-Type': 'application/json' },
//       body: JSON.stringify(userData)
//     });
//     return await response.json();
//   };
  
//   export const getUsers = async () => {
//     const response = await fetch('http://localhost:8000/users');
//     return await response.json();
//   };
  
//   export const submitQuizResults = async (results) => {
//     const response = await fetch('http://localhost:8000/submissions', {
//       method: 'POST',
//       headers: { 'Content-Type': 'application/json' },
//       body: JSON.stringify(results)
//     });
//     return await response.json();
//   };




import axios from 'axios';

const API_URL = 'http://localhost:5000';  // Match your backend port

export const fetchQuizzes = async () => {
  const response = await axios.get(`${API_URL}/api/quizzes`);
  return response.data;
};

export const submitQuiz = async (quizData) => {
  const response = await axios.post(`${API_URL}/api/submit`, quizData);
  return response.data;
};