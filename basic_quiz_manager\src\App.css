/* .App {
  text-align: center;
}

.App-logo {
  height: 40vmin;
  pointer-events: none;
}

@media (prefers-reduced-motion: no-preference) {
  .App-logo {
    animation: App-logo-spin infinite 20s linear;
  }
}

.App-header {
  background-color: #282c34;
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  font-size: calc(10px + 2vmin);
  color: white;
}

.App-link {
  color: #61dafb;
}

@keyframes App-logo-spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
} */



/* Modern Color Palette */
:root {
  --primary: #4361ee;
  --secondary: #3f37c9;
  --accent: #4895ef;
  --light: #f8f9fa;
  --dark: #212529;
  --success: #4cc9f0;
  --warning: #f72585;
}

/* Base Styles */
.App {
  text-align: center;
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  min-height: 100vh;
  background-color: var(--light);
  color: var(--dark);
}

/* Header */
.App-header {
  background: linear-gradient(135deg, var(--primary), var(--secondary));
  padding: 2rem;
  color: white;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

/* Quiz Container */
.quiz-container {
  max-width: 800px;
  margin: 2rem auto;
  padding: 2rem;
  background: white;
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

/* Buttons */
.btn {
  background-color: var(--primary);
  color: white;
  border: none;
  padding: 0.8rem 1.5rem;
  margin: 0.5rem;
  border-radius: 8px;
  font-size: 1rem;
  cursor: pointer;
  transition: all 0.3s ease;
}

.btn:hover {
  background-color: var(--secondary);
  transform: translateY(-2px);
}

.btn-secondary {
  background-color: var(--light);
  color: var(--dark);
  border: 1px solid #ddd;
}

/* Quiz Cards */
.quiz-card {
  background: white;
  border-radius: 10px;
  padding: 1.5rem;
  margin: 1rem 0;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transition: transform 0.3s ease;
}

.quiz-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 6px 12px rgba(0, 0, 0, 0.15);
}

/* Questions */
.question {
  text-align: left;
  margin: 1.5rem 0;
  padding: 1rem;
  border-left: 4px solid var(--accent);
  background-color: #f8f9fa;
  border-radius: 0 8px 8px 0;
}

.options {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1rem;
  margin-top: 1rem;
}

.option {
  padding: 0.8rem;
  border: 2px solid #eee;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.option:hover {
  border-color: var(--accent);
}

.option.selected {
  background-color: var(--primary);
  color: white;
  border-color: var(--primary);
}

/* Progress Bar */
.progress-bar {
  height: 6px;
  background-color: #e9ecef;
  border-radius: 3px;
  margin: 1rem 0;
  overflow: hidden;
}

.progress {
  height: 100%;
  background-color: var(--success);
  transition: width 0.4s ease;
}

/* Responsive Design */
@media (max-width: 768px) {
  .quiz-container {
    padding: 1rem;
    margin: 1rem;
  }
  
  .options {
    grid-template-columns: 1fr;
  }
}

/* Animations */
@keyframes fadeIn {
  from { opacity: 0; transform: translateY(20px); }
  to { opacity: 1; transform: translateY(0); }
}

.quiz-card {
  animation: fadeIn 0.5s ease forwards;
}