import { useEffect, useState } from 'react';
import { fetchQuizzes } from '../services/api';

function QuizList() {
  const [quizzes, setQuizzes] = useState([]);

  useEffect(() => {
    const loadQuizzes = async () => {
      const data = await fetchQuizzes();
      setQuizzes(data);
    };
    loadQuizzes();
  }, []);

  return (
    <div>
      {quizzes.map(quiz => (
        <div key={quiz.id}>{quiz.title}</div>
      ))}
    </div>
  );
}