import React, { useState } from 'react';
import './App.css';

function App() {
  // State for quizzes
  const [quizzes, setQuizzes] = useState([
    { id: 1, title: 'JavaScript Quiz', description: 'Test your JS knowledge' },
    { id: 2, title: 'React Quiz', description: 'React fundamentals' }
  ]);

  // State for questions
  const [questions, setQuestions] = useState([
    { 
      id: 1, 
      quizId: 1, 
      text: 'What is JavaScript?', 
      options: ['A programming language', 'A coffee brand', 'A car model'], 
      correctAnswer: 0 
    },
    { 
      id: 2, 
      quizId: 1, 
      text: 'What is React?', 
      options: ['A JavaScript library', 'A framework', 'A programming language'], 
      correctAnswer: 0 
    }
  ]);

  // UI states
  const [currentView, setCurrentView] = useState('quizList');
  const [currentQuiz, setCurrentQuiz] = useState(null);
  const [newQuiz, setNewQuiz] = useState({ title: '', description: '' });
  const [newQuestion, setNewQuestion] = useState({ 
    text: '', 
    options: ['', '', ''], 
    correctAnswer: 0 
  });
  const [userAnswers, setUserAnswers] = useState({});
  const [userName, setUserName] = useState('');
  const [quizResults, setQuizResults] = useState(null);

  // Quiz management functions
  const addQuiz = () => {
    const newId = quizzes.length > 0 ? Math.max(...quizzes.map(q => q.id)) + 1 : 1;
    setQuizzes([...quizzes, { ...newQuiz, id: newId }]);
    setNewQuiz({ title: '', description: '' });
    setCurrentView('quizList');
  };

  const deleteQuiz = (id) => {
    setQuizzes(quizzes.filter(q => q.id !== id));
    setQuestions(questions.filter(q => q.quizId !== id));
  };

  // Question management functions
  const addQuestion = () => {
    const newId = questions.length > 0 ? Math.max(...questions.map(q => q.id)) + 1 : 1;
    setQuestions([...questions, { 
      ...newQuestion, 
      id: newId, 
      quizId: currentQuiz.id 
    }]);
    setNewQuestion({ text: '', options: ['', '', ''], correctAnswer: 0 });
    setCurrentView('manageQuestions');
  };

  const deleteQuestion = (id) => {
    setQuestions(questions.filter(q => q.id !== id));
  };

  // Quiz taking functions
  const submitQuiz = () => {
    const score = questions
      .filter(q => q.quizId === currentQuiz.id)
      .reduce((total, question) => {
        return userAnswers[question.id] === question.correctAnswer ? total + 1 : total;
      }, 0);
    
    const totalQuestions = questions.filter(q => q.quizId === currentQuiz.id).length;
    setQuizResults({ score, totalQuestions });
    setCurrentView('results');
  };

  // Render different views
  const renderView = () => {
    switch(currentView) {
      case 'quizList':
        return (
          <div className="quiz-list">
            <h1>Quiz Manager</h1>
            <button onClick={() => setCurrentView('addQuiz')}>Add New Quiz</button>
            
            {quizzes.map(quiz => (
              <div key={quiz.id} className="quiz-card">
                <h2>{quiz.title}</h2>
                <p>{quiz.description}</p>
                <button onClick={() => { setCurrentQuiz(quiz); setCurrentView('takeQuiz') }}>
                  Take Quiz
                </button>
                <button onClick={() => { setCurrentQuiz(quiz); setCurrentView('manageQuestions') }}>
                  Manage Questions
                </button>
                <button onClick={() => deleteQuiz(quiz.id)}>Delete Quiz</button>
              </div>
            ))}
          </div>
        );

      case 'addQuiz':
        return (
          <div className="add-quiz">
            <h2>Add New Quiz</h2>
            <input
              placeholder="Quiz Title"
              value={newQuiz.title}
              onChange={(e) => setNewQuiz({...newQuiz, title: e.target.value})}
            />
            <textarea
              placeholder="Description"
              value={newQuiz.description}
              onChange={(e) => setNewQuiz({...newQuiz, description: e.target.value})}
            />
            <button onClick={addQuiz}>Save Quiz</button>
            <button onClick={() => setCurrentView('quizList')}>Cancel</button>
          </div>
        );

      case 'manageQuestions':
        return (
          <div className="manage-questions">
            <h2>Manage Questions for {currentQuiz.title}</h2>
            <button onClick={() => setCurrentView('addQuestion')}>Add New Question</button>
            
            {questions
              .filter(q => q.quizId === currentQuiz.id)
              .map(question => (
                <div key={question.id} className="question-card">
                  <h3>{question.text}</h3>
                  <ul>
                    {question.options.map((option, i) => (
                      <li key={i}>
                        {i === question.correctAnswer ? <strong>{option}</strong> : option}
                      </li>
                    ))}
                  </ul>
                  <button onClick={() => deleteQuestion(question.id)}>Delete</button>
                </div>
              ))}
            
            <button onClick={() => setCurrentView('quizList')}>Back to Quizzes</button>
          </div>
        );

      case 'addQuestion':
        return (
          <div className="add-question">
            <h2>Add New Question</h2>
            <input
              placeholder="Question Text"
              value={newQuestion.text}
              onChange={(e) => setNewQuestion({...newQuestion, text: e.target.value})}
            />
            
            <h3>Options:</h3>
            {newQuestion.options.map((option, i) => (
              <div key={i}>
                <input
                  placeholder={`Option ${i+1}`}
                  value={option}
                  onChange={(e) => {
                    const newOptions = [...newQuestion.options];
                    newOptions[i] = e.target.value;
                    setNewQuestion({...newQuestion, options: newOptions});
                  }}
                />
                <input
                  type="radio"
                  name="correctAnswer"
                  checked={newQuestion.correctAnswer === i}
                  onChange={() => setNewQuestion({...newQuestion, correctAnswer: i})}
                />
                Correct
              </div>
            ))}
            
            <button onClick={addQuestion}>Save Question</button>
            <button onClick={() => setCurrentView('manageQuestions')}>Cancel</button>
          </div>
        );

      case 'takeQuiz':
        return (
          <div className="take-quiz">
            <h1>{currentQuiz.title}</h1>
            <input
              placeholder="Your Name"
              value={userName}
              onChange={(e) => setUserName(e.target.value)}
            />
            
            {questions
              .filter(q => q.quizId === currentQuiz.id)
              .map(question => (
                <div key={question.id} className="quiz-question">
                  <h3>{question.text}</h3>
                  {question.options.map((option, i) => (
                    <div key={i}>
                      <input
                        type="radio"
                        name={`question-${question.id}`}
                        checked={userAnswers[question.id] === i}
                        onChange={() => setUserAnswers({...userAnswers, [question.id]: i})}
                      />
                      {option}
                    </div>
                  ))}
                </div>
              ))}
            
            <button onClick={submitQuiz}>Submit Quiz</button>
            <button onClick={() => setCurrentView('quizList')}>Cancel</button>
          </div>
        );

      case 'results':
        return (
          <div className="results">
            <h1>Quiz Results</h1>
            <h2>{userName}, your score:</h2>
            <p>{quizResults.score} out of {quizResults.totalQuestions} correct</p>
            <button onClick={() => setCurrentView('quizList')}>Back to Quizzes</button>
          </div>
        );

      default:
        return <div>Invalid view</div>;
    }
  };

  return (
    <div className="app">
      {renderView()}
    </div>
  );
}

export default App;




// import React, { useState } from 'react';
// import './App.css';

// function App() {
//   const [currentQuiz, setCurrentQuiz] = useState(null);
//   const [selectedOption, setSelectedOption] = useState(null);
//   const [score, setScore] = useState(0);

//   // Sample quiz data
//   const quizzes = [
//     {
//       id: 1,
//       title: "JavaScript Basics",
//       questions: [
//         {
//           id: 1,
//           text: "What is a closure in JavaScript?",
//           options: [
//             "A function inside another function",
//             "A scope where variables are accessible",
//             "Both A and B",
//             "None of the above"
//           ],
//           correct: 2
//         }
//       ]
//     }
//   ];

//   return (
//     <div className="App">
//       <header className="App-header">
//         <h1>Quiz Manager</h1>
//       </header>

//       <main className="quiz-container">
//         {!currentQuiz ? (
//           <>
//             <h2>Available Quizzes</h2>
//             {quizzes.map(quiz => (
//               <div 
//                 key={quiz.id} 
//                 className="quiz-card"
//                 onClick={() => setCurrentQuiz(quiz)}
//               >
//                 <h3>{quiz.title}</h3>
//                 <p>{quiz.questions.length} questions</p>
//               </div>
//             ))}
//           </>
//         ) : (
//           <>
//             <h2>{currentQuiz.title}</h2>
//             <div className="progress-bar">
//               <div className="progress" style={{ width: '50%' }}></div>
//             </div>
            
//             <div className="question">
//               <h3>Question 1</h3>
//               <p>{currentQuiz.questions[0].text}</p>
              
//               <div className="options">
//                 {currentQuiz.questions[0].options.map((option, index) => (
//                   <div 
//                     key={index}
//                     className={`option ${selectedOption === index ? 'selected' : ''}`}
//                     onClick={() => setSelectedOption(index)}
//                   >
//                     {option}
//                   </div>
//                 ))}
//               </div>
//             </div>

//             <button className="btn">Submit Answer</button>
//             <button 
//               className="btn btn-secondary" 
//               onClick={() => setCurrentQuiz(null)}
//             >
//               Back to Quizzes
//             </button>
//           </>
//         )}
//       </main>
//     </div>
//   );
// }

// export default App;