# import pymysql
# import json
# from datetime import datetime

# class Database:
#     def __init__(self):
#         self.connection = pymysql.connect(
#             host='localhost',
#             user='root',
#             password='Harsh@12345',
#             database='quiz_manager',
#             cursorclass=pymysql.cursors.DictCursor
#         )
    
#     # User Management
#     def register_user(self, email, password, name):
#         with self.connection.cursor() as cursor:
#             try:
#                 sql = "INSERT INTO users (email, password, name) VALUES (%s, %s, %s)"
#                 cursor.execute(sql, (email, password, name))
#                 self.connection.commit()
#                 return cursor.lastrowid
#             except pymysql.IntegrityError:
#                 return None
    
#     def authenticate_user(self, email, password):
#         with self.connection.cursor() as cursor:
#             sql = "SELECT user_id, name FROM users WHERE email=%s AND password=%s"
#             cursor.execute(sql, (email, password))
#             return cursor.fetchone()
    
#     # Quiz Management
#     def create_quiz(self, title, description, created_by):
#         with self.connection.cursor() as cursor:
#             sql = "INSERT INTO quizzes (title, description, created_by) VALUES (%s, %s, %s)"
#             cursor.execute(sql, (title, description, created_by))
#             self.connection.commit()
#             return cursor.lastrowid
    
#     def add_question(self, quiz_id, question_text, options, correct_answer):
#         with self.connection.cursor() as cursor:
#             sql = """INSERT INTO questions 
#                      (quiz_id, question_text, options, correct_answer) 
#                      VALUES (%s, %s, %s, %s)"""
#             cursor.execute(sql, (quiz_id, question_text, json.dumps(options), correct_answer))
#             self.connection.commit()
#             return cursor.lastrowid
    
#     # Submission Handling
#     def submit_quiz(self, user_id, quiz_id, score, answers):
#         with self.connection.cursor() as cursor:
#             sql = """INSERT INTO submissions 
#                      (user_id, quiz_id, score, answers) 
#                      VALUES (%s, %s, %s, %s)"""
#             cursor.execute(sql, (user_id, quiz_id, score, json.dumps(answers)))
#             self.connection.commit()
#             return cursor.lastrowid
    
#     # Data Retrieval
#     def get_quiz(self, quiz_id):
#         with self.connection.cursor() as cursor:
#             sql = "SELECT * FROM quizzes WHERE quiz_id=%s"
#             cursor.execute(sql, (quiz_id,))
#             return cursor.fetchone()
    
#     def get_quiz_questions(self, quiz_id):
#         with self.connection.cursor() as cursor:
#             sql = "SELECT * FROM questions WHERE quiz_id=%s"
#             cursor.execute(sql, (quiz_id,))
#             questions = cursor.fetchall()
#             for q in questions:
#                 q['options'] = json.loads(q['options'])
#             return questions
    
#     def close(self):
#         self.connection.close()