import axios from 'axios';

const API_URL = 'http://localhost:5000';

export const registerUser = async (userData) => {
    return await axios.post(`${API_URL}/register`, userData);
};

export const loginUser = async (credentials) => {
    return await axios.post(`${API_URL}/login`, credentials);
};

export const createQuiz = async (quizData, token) => {
    return await axios.post(`${API_URL}/quizzes`, quizData, {
        headers: { 'Authorization': `Bearer ${token}` }
    });
};

export const getQuiz = async (quizId) => {
    return await axios.get(`${API_URL}/quizzes/${quizId}`);
};

export const submitQuiz = async (submissionData, token) => {
    return await axios.post(`${API_URL}/submit`, submissionData, {
        headers: { 'Authorization': `Bearer ${token}` }
    });
};