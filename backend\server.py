import sqlite3
from http.server import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPServer
import json
from urllib.parse import urlparse, parse_qs

# import pymysql

# database = pymysql.connect ("localhost","root", "Harsh@12345", "quiz_manager")
# c=database.cursor();


 # User Management
def register_user(self, email, password, name):
        with self.connection.cursor() as cursor:
            try:
                sql = "INSERT INTO users (email, password, name) VALUES (%s, %s, %s)"
                cursor.execute(sql, (email, password, name))
                self.connection.commit()
                return cursor.lastrowid
            except sqlite3.IntegrityError:
                return None
    
def authenticate_user(self, email, password):
        with self.connection.cursor() as cursor:
            sql = "SELECT user_id, name FROM users WHERE email=%s AND password=%s"
            cursor.execute(sql, (email, password))
            return cursor.fetchone()
    
#     # Quiz Management
def create_quiz(self, title, description, created_by):
        with self.connection.cursor() as cursor:
            sql = "INSERT INTO quizzes (title, description, created_by) VALUES (%s, %s, %s)"
            cursor.execute(sql, (title, description, created_by))
            self.connection.commit()
            return cursor.lastrowid
    
def add_question(self, quiz_id, question_text, options, correct_answer):
        with self.connection.cursor() as cursor:
            sql = """INSERT INTO questions 
                     (quiz_id, question_text, options, correct_answer) 
                     VALUES (%s, %s, %s, %s)"""
            cursor.execute(sql, (quiz_id, question_text, json.dumps(options), correct_answer))
            self.connection.commit()
            return cursor.lastrowid
    
    # Submission Handling
def submit_quiz(self, user_id, quiz_id, score, answers):
        with self.connection.cursor() as cursor:
            sql = """INSERT INTO submissions 
                     (user_id, quiz_id, score, answers) 
                     VALUES (%s, %s, %s, %s)"""
            cursor.execute(sql, (user_id, quiz_id, score, json.dumps(answers)))
            self.connection.commit()
            return cursor.lastrowid
    
    # Data Retrieval
def get_quiz(self, quiz_id):
        with self.connection.cursor() as cursor:
            sql = "SELECT * FROM quizzes WHERE quiz_id=%s"
            cursor.execute(sql, (quiz_id,))
            return cursor.fetchone()
    
def get_quiz_questions(self, quiz_id):
        with self.connection.cursor() as cursor:
            sql = "SELECT * FROM questions WHERE quiz_id=%s"
            cursor.execute(sql, (quiz_id,))
            questions = cursor.fetchall()
            for q in questions:
                q['options'] = json.loads(q['options'])
            return questions
    
def close(self):
        self.connection.close()


# # Database setup
DB_NAME = "quiz_database.db"

def init_db():
    conn = sqlite3.connect(DB_NAME)
    c = conn.cursor()
    
    # Create tables
    c.execute('''CREATE TABLE IF NOT EXISTS quizzes
                 (id INTEGER PRIMARY KEY AUTOINCREMENT,
                  title TEXT NOT NULL,
                  description TEXT)''')
    
    c.execute('''CREATE TABLE IF NOT EXISTS questions
                 (id INTEGER PRIMARY KEY AUTOINCREMENT,
                  quiz_id INTEGER NOT NULL,
                  text TEXT NOT NULL,
                  options TEXT NOT NULL,  -- JSON array
                  correct_answer INTEGER NOT NULL,
                  FOREIGN KEY (quiz_id) REFERENCES quizzes(id))''')
    
    c.execute('''CREATE TABLE IF NOT EXISTS users
                 (id INTEGER PRIMARY KEY AUTOINCREMENT,
                  name TEXT NOT NULL UNIQUE,
                  email TEXT)''')
    
    c.execute('''CREATE TABLE IF NOT EXISTS submissions
                 (id INTEGER PRIMARY KEY AUTOINCREMENT,
                  user_id INTEGER NOT NULL,
                  quiz_id INTEGER NOT NULL,
                  score INTEGER NOT NULL,
                  answers TEXT NOT NULL,  -- JSON object
                  timestamp DATETIME DEFAULT CURRENT_TIMESTAMP,
                  FOREIGN KEY (user_id) REFERENCES users(id),
                  FOREIGN KEY (quiz_id) REFERENCES quizzes(id))''')
    
    conn.commit()
    conn.close()

class QuizHandler(BaseHTTPRequestHandler):
    def _set_headers(self, status=200):
        self.send_response(status)
        self.send_header('Content-type', 'application/json')
        self.send_header('Access-Control-Allow-Origin', '*')
        self.end_headers()
    
    def _get_db_connection(self):
        conn = sqlite3.connect(DB_NAME)
        conn.row_factory = sqlite3.Row  # For dictionary-like access
        return conn

    # USER MANAGEMENT ENDPOINTS
    def do_POST(self):
        path = urlparse(self.path).path
        content_length = int(self.headers['Content-Length'])
        data = json.loads(self.rfile.read(content_length))
        
        conn = self._get_db_connection()
        c = conn.cursor()
        
        if path == "/users":
            try:
                c.execute("INSERT INTO users (name, email) VALUES (?, ?)",
                         (data['name'], data.get('email')))
                user_id = c.lastrowid
                conn.commit()
                self._set_headers(201)
                self.wfile.write(json.dumps({"id": user_id, "name": data['name']}).encode())
            except sqlite3.IntegrityError:
                self._set_headers(400)
                self.wfile.write(json.dumps({"error": "Username already exists"}).encode())
        
        elif path == "/submissions":
            # Store quiz submission with user reference
            c.execute("INSERT INTO submissions (user_id, quiz_id, score, answers) VALUES (?, ?, ?, ?)",
                     (data['user_id'], data['quiz_id'], data['score'], json.dumps(data['answers'])))
            conn.commit()
            self._set_headers(201)
            self.wfile.write(json.dumps({"message": "Submission saved"}).encode())
        
        conn.close()

    def do_GET(self):
        path = urlparse(self.path).path
        conn = self._get_db_connection()
        c = conn.cursor()
        
        if path == "/users":
            c.execute("SELECT id, name, email FROM users")
            users = [dict(row) for row in c.fetchall()]
            self._set_headers()
            self.wfile.write(json.dumps(users).encode())
        
        elif path.startswith("/users/"):
            user_id = path.split("/")[2]
            c.execute("SELECT id, name, email FROM users WHERE id = ?", (user_id,))
            user = c.fetchone()
            if user:
                self._set_headers()
                self.wfile.write(json.dumps(dict(user)).encode())
            else:
                self._set_headers(404)
        
        conn.close()

def run(server_class=HTTPServer, handler_class=QuizHandler, port=8000):
    init_db()  # Initialize database on startup
    server_address = ('', port)
    httpd = server_class(server_address, handler_class)
    print(f'Server running on port {port}...')
    httpd.serve_forever()

if __name__ == '__main__':
    run()





import mysql.connector

# Connect to database
conn = mysql.connector.connect(
    host="localhost",
    user="root",
    password="Harsh@12345",
    database="quiz_manager"
)

# Create a cursor object to execute SQL queries
cursor = conn.cursor()

# Example query: fetch all rows from 'questions' table
cursor.execute("SELECT * FROM questions")

# Fetch and print the results
results = cursor.fetchall()
for row in results:
    print(row)

# Close the connection
conn.close()





# import pymysql
# import json
# from datetime import datetime

# class Database:
#     def __init__(self):
#         self.connection = pymysql.connect(
#             host='localhost',
#             user='root',
#             password='Harsh@12345',
#             database='quiz_manager',
#             cursorclass=pymysql.cursors.DictCursor
#         )
    
#     # User Management
#     def register_user(self, email, password, name):
#         with self.connection.cursor() as cursor:
#             try:
#                 sql = "INSERT INTO users (email, password, name) VALUES (%s, %s, %s)"
#                 cursor.execute(sql, (email, password, name))
#                 self.connection.commit()
#                 return cursor.lastrowid
#             except pymysql.IntegrityError:
#                 return None
    
#     def authenticate_user(self, email, password):
#         with self.connection.cursor() as cursor:
#             sql = "SELECT user_id, name FROM users WHERE email=%s AND password=%s"
#             cursor.execute(sql, (email, password))
#             return cursor.fetchone()
    
#     # Quiz Management
#     def create_quiz(self, title, description, created_by):
#         with self.connection.cursor() as cursor:
#             sql = "INSERT INTO quizzes (title, description, created_by) VALUES (%s, %s, %s)"
#             cursor.execute(sql, (title, description, created_by))
#             self.connection.commit()
#             return cursor.lastrowid
    
#     def add_question(self, quiz_id, question_text, options, correct_answer):
#         with self.connection.cursor() as cursor:
#             sql = """INSERT INTO questions 
#                      (quiz_id, question_text, options, correct_answer) 
#                      VALUES (%s, %s, %s, %s)"""
#             cursor.execute(sql, (quiz_id, question_text, json.dumps(options), correct_answer))
#             self.connection.commit()
#             return cursor.lastrowid
    
#     # Submission Handling
#     def submit_quiz(self, user_id, quiz_id, score, answers):
#         with self.connection.cursor() as cursor:
#             sql = """INSERT INTO submissions 
#                      (user_id, quiz_id, score, answers) 
#                      VALUES (%s, %s, %s, %s)"""
#             cursor.execute(sql, (user_id, quiz_id, score, json.dumps(answers)))
#             self.connection.commit()
#             return cursor.lastrowid
    
#     # Data Retrieval
#     def get_quiz(self, quiz_id):
#         with self.connection.cursor() as cursor:
#             sql = "SELECT * FROM quizzes WHERE quiz_id=%s"
#             cursor.execute(sql, (quiz_id,))
#             return cursor.fetchone()
    
#     def get_quiz_questions(self, quiz_id):
#         with self.connection.cursor() as cursor:
#             sql = "SELECT * FROM questions WHERE quiz_id=%s"
#             cursor.execute(sql, (quiz_id,))
#             questions = cursor.fetchall()
#             for q in questions:
#                 q['options'] = json.loads(q['options'])
#             return questions
    
#     def close(self):
#         self.connection.close()
